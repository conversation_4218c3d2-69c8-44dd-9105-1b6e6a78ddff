import os
import cv2
import numpy as np
from ultralytics import YOLO
import matplotlib.pyplot as plt
from pathlib import Path
import shutil
import time

def load_models():
    """Charger les modèles YOLOv8n pré-entraîné et personnalisé"""
    # Modèle pré-entraîné
    model_pretrained = YOLO('yolov8n.pt')

    # Modèle personnalisé
    custom_model_path = r"C:\Users\<USER>\Desktop\result\last (1).pt"
    model_custom = YOLO(custom_model_path)

    return model_pretrained, model_custom

def get_images(images_dir, max_images=None):
    """Récupérer les images du dossier (toutes ou un nombre limité)"""
    images_path = Path(images_dir)
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']

    images = []
    for ext in image_extensions:
        images.extend(list(images_path.glob(f'*{ext}')))
        images.extend(list(images_path.glob(f'*{ext.upper()}')))

    # Trier les images
    images = sorted(images)

    # Limiter le nombre d'images si spécifié
    if max_images is not None:
        images = images[:max_images]

    return images

def draw_predictions(image, results, title):
    """Dessiner les prédictions sur l'image"""
    img_copy = image.copy()

    if len(results) > 0 and results[0].boxes is not None:
        boxes = results[0].boxes

        for box in boxes:
            # Coordonnées de la boîte
            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)

            # Confiance
            conf = box.conf[0].cpu().numpy()

            # Classe
            cls = int(box.cls[0].cpu().numpy())
            class_name = results[0].names[cls]

            # Dessiner la boîte
            cv2.rectangle(img_copy, (x1, y1), (x2, y2), (0, 255, 0), 2)

            # Ajouter le texte
            label = f'{class_name}: {conf:.2f}'
            cv2.putText(img_copy, label, (x1, y1-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

    return img_copy

def create_comparison_visualization(image_path, model_pretrained, model_custom, output_dir, image_idx):
    """Créer une visualisation comparative pour une image"""
    # Charger l'image
    image = cv2.imread(str(image_path))
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

    # Faire les prédictions
    results_pretrained = model_pretrained(image_path)
    results_custom = model_custom(image_path)

    # Dessiner les prédictions
    img_pretrained = draw_predictions(image_rgb, results_pretrained, "YOLOv8n Pré-entraîné")
    img_custom = draw_predictions(image_rgb, results_custom, "YOLOv8n Custom")

    # Créer la figure avec 2 sous-graphiques
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # Afficher les images
    ax1.imshow(img_pretrained)
    ax1.set_title(f"YOLOv8n Pré-entraîné\nImage {image_idx + 1}: {image_path.name}", fontsize=12)
    ax1.axis('off')

    ax2.imshow(img_custom)
    ax2.set_title(f"YOLOv8n Custom\nImage {image_idx + 1}: {image_path.name}", fontsize=12)
    ax2.axis('off')

    # Ajuster l'espacement
    plt.tight_layout()

    # Sauvegarder
    output_path = output_dir / f"comparison_{image_idx + 1:02d}_{image_path.stem}.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    return output_path

def main():
    """Fonction principale"""
    # Chemins
    images_dir = r"C:\Users\<USER>\Desktop\formatkiitidataset\train\images"
    output_base_dir = Path(r"C:\Users\<USER>\Desktop\yolo_comparison_results")

    # Demander à l'utilisateur combien d'images traiter
    print("🔍 Configuration du traitement")
    print("Combien d'images voulez-vous traiter ?")
    print("1. Les 10 premières images (rapide - ~2 minutes)")
    print("2. Les 100 premières images (moyen - ~20 minutes)")
    print("3. Les 500 premières images (long - ~1h30)")
    print("4. Toutes les images (~9600 images - ~15+ heures)")

    choice = input("Votre choix (1-4): ").strip()

    if choice == "1":
        max_images = 10
    elif choice == "2":
        max_images = 100
    elif choice == "3":
        max_images = 500
    elif choice == "4":
        max_images = None
    else:
        print("❌ Choix invalide, utilisation de 10 images par défaut")
        max_images = 10

    # Créer le dossier de sortie
    if output_base_dir.exists():
        shutil.rmtree(output_base_dir)
    output_base_dir.mkdir(parents=True, exist_ok=True)

    print("Chargement des modèles...")
    try:
        model_pretrained, model_custom = load_models()
        print("✓ Modèles chargés avec succès")
    except Exception as e:
        print(f"❌ Erreur lors du chargement des modèles: {e}")
        return

    print("Récupération des images...")
    try:
        images = get_images(images_dir, max_images)
        if not images:
            print("❌ Aucune image trouvée dans le dossier spécifié")
            return
        print(f"✓ {len(images)} images trouvées")
    except Exception as e:
        print(f"❌ Erreur lors de la récupération des images: {e}")
        return

    print("Génération des comparaisons...")
    successful_comparisons = 0
    start_time = time.time()

    for idx, image_path in enumerate(images):
        try:
            # Calcul du temps estimé
            if idx > 0:
                elapsed_time = time.time() - start_time
                avg_time_per_image = elapsed_time / idx
                remaining_images = len(images) - idx
                estimated_remaining = avg_time_per_image * remaining_images
                print(f"Traitement de l'image {idx + 1}/{len(images)}: {image_path.name} "
                      f"(ETA: {estimated_remaining/60:.1f}min)")
            else:
                print(f"Traitement de l'image {idx + 1}/{len(images)}: {image_path.name}")

            output_path = create_comparison_visualization(
                image_path, model_pretrained, model_custom, output_base_dir, idx
            )
            print(f"✓ Comparaison sauvegardée: {output_path}")
            successful_comparisons += 1

        except Exception as e:
            print(f"❌ Erreur lors du traitement de {image_path.name}: {e}")
            continue

    # Calcul du temps total
    total_time = time.time() - start_time
    avg_time_per_image = total_time / len(images) if len(images) > 0 else 0

    print(f"\n🎉 Traitement terminé!")
    print(f"📊 {successful_comparisons}/{len(images)} comparaisons générées avec succès")
    print(f"⏱️  Temps total: {total_time/60:.1f} minutes")
    print(f"⚡ Temps moyen par image: {avg_time_per_image:.2f} secondes")
    print(f"📁 Résultats sauvegardés dans: {output_base_dir}")

    # Créer un fichier récapitulatif
    custom_model_path = r"C:\Users\<USER>\Desktop\result\last (1).pt"
    summary_file = output_base_dir / "summary.txt"
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("RÉSUMÉ DE LA COMPARAISON YOLO\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"Modèle pré-entraîné: YOLOv8n\n")
        f.write(f"Modèle personnalisé: {custom_model_path}\n")
        f.write(f"Dossier d'images: {images_dir}\n")
        f.write(f"Nombre total d'images: {len(images)}\n")
        f.write(f"Nombre d'images traitées avec succès: {successful_comparisons}\n")
        f.write(f"Taux de réussite: {(successful_comparisons/len(images)*100):.1f}%\n")
        f.write(f"Temps total de traitement: {total_time/60:.1f} minutes\n")
        f.write(f"Temps moyen par image: {avg_time_per_image:.2f} secondes\n")
        f.write(f"Date de génération: {np.datetime64('now').astype(str)}\n")

if __name__ == "__main__":
    main()
