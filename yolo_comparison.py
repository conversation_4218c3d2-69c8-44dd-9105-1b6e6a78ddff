import os
import cv2
import numpy as np
from ultralytics import YOLO
import matplotlib.pyplot as plt
from pathlib import Path
import shutil

def load_models():
    """Charger les modèles YOLOv8n pré-entraîné et personnalisé"""
    # Modèle pré-entraîné
    model_pretrained = YOLO('yolov8n.pt')

    # Modèle personnalisé
    custom_model_path = r"C:\Users\<USER>\Desktop\result\last (1).pt"
    model_custom = YOLO(custom_model_path)

    return model_pretrained, model_custom

def get_first_10_images(images_dir):
    """Récupérer les 10 premières images du dossier"""
    images_path = Path(images_dir)
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']

    images = []
    for ext in image_extensions:
        images.extend(list(images_path.glob(f'*{ext}')))
        images.extend(list(images_path.glob(f'*{ext.upper()}')))

    # Trier et prendre les 10 premières
    images = sorted(images)[:10]
    return images

def draw_predictions(image, results, title):
    """Dessiner les prédictions sur l'image"""
    img_copy = image.copy()

    if len(results) > 0 and results[0].boxes is not None:
        boxes = results[0].boxes

        for box in boxes:
            # Coordonnées de la boîte
            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)

            # Confiance
            conf = box.conf[0].cpu().numpy()

            # Classe
            cls = int(box.cls[0].cpu().numpy())
            class_name = results[0].names[cls]

            # Dessiner la boîte
            cv2.rectangle(img_copy, (x1, y1), (x2, y2), (0, 255, 0), 2)

            # Ajouter le texte
            label = f'{class_name}: {conf:.2f}'
            cv2.putText(img_copy, label, (x1, y1-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

    return img_copy

def create_comparison_visualization(image_path, model_pretrained, model_custom, output_dir, image_idx):
    """Créer une visualisation comparative pour une image"""
    # Charger l'image
    image = cv2.imread(str(image_path))
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

    # Faire les prédictions
    results_pretrained = model_pretrained(image_path)
    results_custom = model_custom(image_path)

    # Dessiner les prédictions
    img_pretrained = draw_predictions(image_rgb, results_pretrained, "YOLOv8n Pré-entraîné")
    img_custom = draw_predictions(image_rgb, results_custom, "YOLOv8n Custom")

    # Créer la figure avec 2 sous-graphiques
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # Afficher les images
    ax1.imshow(img_pretrained)
    ax1.set_title(f"YOLOv8n Pré-entraîné\nImage {image_idx + 1}: {image_path.name}", fontsize=12)
    ax1.axis('off')

    ax2.imshow(img_custom)
    ax2.set_title(f"YOLOv8n Custom\nImage {image_idx + 1}: {image_path.name}", fontsize=12)
    ax2.axis('off')

    # Ajuster l'espacement
    plt.tight_layout()

    # Sauvegarder
    output_path = output_dir / f"comparison_{image_idx + 1:02d}_{image_path.stem}.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    return output_path

def main():
    """Fonction principale"""
    # Chemins
    images_dir = r"C:\Users\<USER>\Desktop\formatkiitidataset\train\images"
    output_base_dir = Path(r"C:\Users\<USER>\Desktop\yolo_comparison_results")

    # Créer le dossier de sortie
    if output_base_dir.exists():
        shutil.rmtree(output_base_dir)
    output_base_dir.mkdir(parents=True, exist_ok=True)

    print("Chargement des modèles...")
    try:
        model_pretrained, model_custom = load_models()
        print("✓ Modèles chargés avec succès")
    except Exception as e:
        print(f"❌ Erreur lors du chargement des modèles: {e}")
        return

    print("Récupération des images...")
    try:
        images = get_first_10_images(images_dir)
        if not images:
            print("❌ Aucune image trouvée dans le dossier spécifié")
            return
        print(f"✓ {len(images)} images trouvées")
    except Exception as e:
        print(f"❌ Erreur lors de la récupération des images: {e}")
        return

    print("Génération des comparaisons...")
    successful_comparisons = 0

    for idx, image_path in enumerate(images):
        try:
            print(f"Traitement de l'image {idx + 1}/10: {image_path.name}")
            output_path = create_comparison_visualization(
                image_path, model_pretrained, model_custom, output_base_dir, idx
            )
            print(f"✓ Comparaison sauvegardée: {output_path}")
            successful_comparisons += 1

        except Exception as e:
            print(f"❌ Erreur lors du traitement de {image_path.name}: {e}")
            continue

    print(f"\n🎉 Traitement terminé!")
    print(f"📊 {successful_comparisons}/{len(images)} comparaisons générées avec succès")
    print(f"📁 Résultats sauvegardés dans: {output_base_dir}")

    # Créer un fichier récapitulatif
    summary_file = output_base_dir / "summary.txt"
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("RÉSUMÉ DE LA COMPARAISON YOLO\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"Modèle pré-entraîné: YOLOv8n\n")
        f.write(f"Modèle personnalisé: {r'C:\Users\<USER>\Desktop\result\last (1).pt'}\n")
        f.write(f"Dossier d'images: {images_dir}\n")
        f.write(f"Nombre d'images traitées: {successful_comparisons}/{len(images)}\n")
        f.write(f"Date de génération: {np.datetime64('now').astype(str)}\n")

if __name__ == "__main__":
    main()
